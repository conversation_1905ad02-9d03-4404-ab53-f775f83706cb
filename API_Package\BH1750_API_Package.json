{"BH1750_Interface": {"status_enum": {"BH1750_StatusTypeDef": ["BH1750_OK", "BH1750_ERROR_TIMEOUT", "BH1750_ERROR_I2C", "BH1750_ERROR_NOT_RESPONDING"]}, "functions": [{"name": "bh1750_init", "description": "初始化传感器并配置I2C接口。CRITICAL: 必须在初始化后立即设置测量模式，否则传感器无法正常工作！", "return_type": "BH1750_StatusTypeDef", "parameters": [{"name": "i2c_port", "type": "i2c_port_t", "description": "I2C端口号(ESP32对应I2C_NUM_0/I2C_NUM_1)"}, {"name": "sda_pin", "type": "gpio_num_t", "description": "SDA引脚编号"}, {"name": "scl_pin", "type": "gpio_num_t", "description": "SCL引脚编号"}, {"name": "addr_pin_state", "type": "bool", "description": "ADDR引脚电平状态(true=高电平，false=低电平)"}], "mandatory_sequence": "MUST call bh1750_set_mode() immediately after successful initialization"}, {"name": "bh1750_set_mode", "description": "设置传感器测量模式。CRITICAL: 这是必须调用的函数！BH1750在上电后必须设置测量模式才能正常工作。推荐使用连续测量模式以避免每次读取前重新设置。", "return_type": "BH1750_StatusTypeDef", "parameters": [{"name": "mode", "type": "bh1750_mode_t", "description": "测量模式枚举值。推荐使用BH1750_CONTINUOUS_HIGH_RES_MODE进行连续高精度测量"}], "usage_notes": ["MANDATORY: Must be called after bh1750_init() and before first bh1750_read_lux()", "Continuous modes: Set once, read multiple times", "One-time modes: Must set before each read operation", "High-res modes need 120ms conversion time, low-res modes need 16ms"]}, {"name": "bh1750_read_lux", "description": "读取光照强度值，当处理错误情况使用nan时一定要优先声明NAN", "return_type": "float", "parameters": []}, {"name": "bh1750_power_down", "description": "进入低功耗模式", "return_type": "BH1750_StatusTypeDef", "parameters": []}, {"name": "bh1750_reset", "description": "软件复位传感器", "return_type": "BH1750_StatusTypeDef", "parameters": []}], "data_types": {"bh1750_mode_t": ["BH1750_CONTINUOUS_HIGH_RES_MODE", "BH1750_CONTINUOUS_HIGH_RES_MODE_2", "BH1750_CONTINUOUS_LOW_RES_MODE", "BH1750_ONE_TIME_HIGH_RES_MODE", "BH1750_ONE_TIME_HIGH_RES_MODE_2", "BH1750_ONE_TIME_LOW_RES_MODE"]}, "hardware_config": {"i2c_frequency": 100000, "default_address": {"ADDR_LOW": "0x23", "ADDR_HIGH": "0x5C"}, "measurement_time": {"HIGH_RES_MODE": 120, "LOW_RES_MODE": 16}}, "error_handling": {"retry_count": 3, "timeout_threshold": 200}, "critical_usage_constraints": {"initialization_sequence": ["1. Call bh1750_init() to initialize I2C and power on sensor", "2. MANDATORY: Call bh1750_set_mode() to set measurement mode", "3. Wait for conversion time (120ms for high-res, 16ms for low-res)", "4. Now bh1750_read_lux() can be called successfully"], "common_errors": ["ERROR: Reading without setting mode - will return stale/invalid data", "ERROR: Not waiting for conversion time - will return incorrect values", "ERROR: Using one-time mode without re-setting before each read"], "recommended_pattern": {"setup": "bh1750_init() -> bh1750_set_mode(BH1750_CONTINUOUS_HIGH_RES_MODE) -> delay(120ms)", "loop": "bh1750_read_lux() every 5+ seconds (no need to re-set mode in continuous mode)"}}}}