{"HC-SR04_Interface": {"functions": [{"name": "hcsr04_init", "description": "Initializes the HC-SR04 sensor with the specified trigger and echo pins.", "return_type": "int", "parameters": [{"name": "trigger_pin", "type": "int"}, {"name": "echo_pin", "type": "int"}]}, {"name": "hcsr04_read_distance_cm", "description": "Returns the measured distance in centimeters. IMPLEMENTATION REQUIRED: 1) Send 10μs HIGH pulse on trigger pin 2) Wait for echo pin HIGH 3) Measure echo pulse duration 4) Calculate distance=(duration*0.0343)/2. Returns NAN on timeout/failure.", "return_type": "float", "parameters": []}, {"name": "hcsr04_read_distance_mm", "description": "Returns the measured distance in millimeters.", "return_type": "float", "parameters": []}, {"name": "hcsr04_set_timeout_us", "description": "Sets the echo pulse timeout in microseconds to avoid infinite waits.", "return_type": "void", "parameters": [{"name": "timeout_us", "type": "uint32_t"}]}], "implementation_template": {"critical_steps": ["1. Generate 10μs trigger pulse: gpio_set_level(trig, 1); delay_us(10); gpio_set_level(trig, 0);", "2. Wait for echo HIGH with timeout check", "3. Record start time when echo goes HIGH", "4. Wait for echo LOW with timeout check", "5. Calculate duration = end_time - start_time", "6. Return distance = (duration * 0.0343) / 2.0"], "common_errors": ["Missing trigger pulse generation", "Incorrect timeout logic (start=0 causes immediate timeout)", "Not checking for NAN before JSON serialization"], "critical_bug_prevention": {"timeout_logic_error": {"description": "CRITICAL: Timer initialization must happen BEFORE timeout check", "wrong_pattern": "int64_t start = 0; while(condition) { if(timer() - start > timeout) break; } start = timer();", "correct_pattern": "int64_t start_wait = timer(); while(condition) { if(timer() - start_wait > timeout) break; } int64_t pulse_start = timer();", "explanation": "The start variable must be initialized with current time BEFORE entering the wait loop, not after the condition is met"}, "echo_measurement": {"description": "Proper echo pulse measurement requires two separate timers", "implementation": "Use start_wait for timeout during echo HIGH wait, then pulse_start/pulse_end for actual pulse duration measurement"}}, "validated_implementation": {"measure_function": "static inline int64_t measure_echo_pulse_us(void) { int64_t start_wait = esp_timer_get_time(); while (gpio_get_level(echo_pin) == 0) { if ((esp_timer_get_time() - start_wait) > timeout) return -1; } int64_t pulse_start = esp_timer_get_time(); while (gpio_get_level(echo_pin) == 1) { if ((esp_timer_get_time() - pulse_start) > timeout) return -1; } int64_t pulse_end = esp_timer_get_time(); return (pulse_end - pulse_start); }"}}}}