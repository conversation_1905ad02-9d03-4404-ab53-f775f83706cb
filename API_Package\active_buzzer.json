{"有源蜂鸣器_Interface": {"functions": [{"name": "buzzer_init", "description": "Initializes the active buzzer on a specific GPIO pin.", "return_type": "void", "parameters": [{"name": "pin", "type": "int", "description": "The GPIO pin number where the buzzer is connected."}]}, {"name": "buzzer_on", "description": "Turns the active buzzer on by setting the GPIO pin to low level.", "return_type": "void", "parameters": []}, {"name": "buzzer_off", "description": "Turns the active buzzer off by setting the GPIO pin to high level.", "return_type": "void", "parameters": []}, {"name": "buzzer_beep", "description": "Produces a beep sound for a specified duration.", "return_type": "void", "parameters": [{"name": "duration_ms", "type": "int", "description": "The duration of the beep in milliseconds."}]}, {"name": "buzzer_set_frequency", "description": "Sets the frequency of the buzzer sound (if supported by hardware).", "return_type": "void", "parameters": [{"name": "frequency_hz", "type": "int", "description": "The frequency of the sound in Hertz."}]}]}}