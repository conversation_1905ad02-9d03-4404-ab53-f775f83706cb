{"有源蜂鸣器_Interface": {"state_management_note": "IMPORTANT: Use separate variables - buzzerEnabled (control) and buzzerActive (actual state). Send buzzerActive to data points, initialize buzzerEnabled=true.", "functions": [{"name": "buzzer_init", "description": "Initializes the active buzzer on a specific GPIO pin. For low-level triggered buzzers, initializes pin to HIGH (inactive state).", "return_type": "void", "parameters": [{"name": "pin", "type": "int"}]}, {"name": "buzzer_on", "description": "Turns the buzzer on (continuous tone). For low-level triggered buzzers, this should output LOW; for high-level triggered buzzers, this should output HIGH.", "return_type": "void", "parameters": []}, {"name": "buzzer_off", "description": "Turns the buzzer off. For low-level triggered buzzers, this should output HIGH; for high-level triggered buzzers, this should output LOW.", "return_type": "void", "parameters": []}, {"name": "buzzer_beep", "description": "Generates a beep of specified duration in milliseconds.", "return_type": "void", "parameters": [{"name": "duration_ms", "type": "uint32_t"}]}, {"name": "buzzer_pattern", "description": "Plays a pattern defined by an array of on/off durations.", "return_type": "void", "parameters": [{"name": "pattern", "type": "const uint32_t*"}, {"name": "length", "type": "uint32_t"}]}]}}