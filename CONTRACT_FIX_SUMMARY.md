# 契约修复总结

## 问题诊断

您观察到的现象完全正确："问题似乎常常出现在第二个设备的生成"。这直接指向了问题的根源：

### 信息流断裂分析

**问题根源**：`unified_communication_contract` 只传递了通信的"地址"（Topic），而没有传递通信的"语言"（Payload Schema / JSON Key）。

**断裂点**：当工作流从生成第一个设备（光照采集端）切换到第二个设备（报警器）时，`device_dispatcher_node` 的状态隔离机制虽然防止了状态污染，但也意外地丢弃了本应共享的关键信息——第一个设备最终确定的数据格式。

### 信息旅程追踪

1. **阶段一：生成"光照采集端"（生产者）**
   - `plan_enrichment_node`: 正确分配主题 `/smart_system/light_collector/data`
   - `dp_designer_node`: 生成 `device_dp_contract`，包含 `code: illuminance_lux`
   - `developer_node`: 忠实执行契约，生成 `doc["illuminance_lux"] = lux;`

2. **阶段二：设备切换 (device_dispatcher_node)**
   - 创建全新的、干净的 state
   - 根据白名单保留 `unified_communication_contract`
   - **关键断点**：`device_dp_contract`（包含 `illuminance_lux`）被丢弃
   - `illuminance_lux` 信息没能被提升并合并到 `unified_communication_contract` 中

3. **阶段三：生成"报警器"（消费者）**
   - `dp_designer_node`: 为报警器生成自己的 DP（如 `alarm_active`, `illuminance`）
   - `developer_node`: 只知道订阅 `/smart_system/light_collector/data`，但不知道数据格式
   - **信息孤岛**：变成盲人，知道去哪里取信，但不知道信的语言

## 解决方案实施

### 1. 添加契约调试器节点 ✅

**文件**: `app/langgraph_def/graph_builder.py`

```python
def contract_debugger_node(state: AgentState) -> Dict:
    """
    一个简单的调试节点，用于在关键步骤打印所有可用的契约信息。
    """
    print("\n" + "="*25 + " [CONTRACT DEBUGGER] " + "="*25)
    current_device = state.get('current_device_task', {}).get('device_role', 'N/A')
    print(f"DEVICE CONTEXT: '{current_device}'")

    # 打印统一通信契约
    unified_contract = state.get('unified_communication_contract')
    if unified_contract:
        print("\n--- ✅ [Unified Communication Contract] ---")
        pprint(unified_contract)
    else:
        print("\n--- ❌ [Unified Communication Contract]: NOT FOUND ---")

    # 打印当前设备的DP契约
    device_dp_contract = state.get('device_dp_contract')
    if device_dp_contract:
        print("\n--- ✅ [Current Device DP Contract] ---")
        pprint(device_dp_contract)
    else:
        print("\n--- ❌ [Current Device DP Contract]: NOT FOUND ---")

    # 打印当前设备的任务契约
    task_contract = state.get('current_device_task', {}).get('task_contract')
    if task_contract:
        print("\n--- ✅ [Current Device Task Contract] ---")
        pprint(task_contract)
    else:
        print("\n--- ❌ [Current Device Task Contract]: NOT FOUND ---")
        
    print("="*70 + "\n")
    return {}
```

**图连接修改**:
```python
workflow.add_edge("api_designer", "contract_debugger")
workflow.add_edge("contract_debugger", "developer")
```

### 2. 增强 DP 设计器节点 ✅

**核心修复**：将 DP 信息合并到统一通信契约中，建立完整的数据契约

```python
# 【核心修复】将DP信息合并到统一通信契约中，建立完整的数据契约
if 'unified_communication_contract' in state:
    unified_contract = state['unified_communication_contract'].copy()
    
    # 如果还没有schema字段，创建它
    if 'schema' not in unified_contract:
        unified_contract['schema'] = {}
    
    # 为当前设备的发布主题添加payload schema
    topic_map = unified_contract.get('topic_map', {})
    device_topics = topic_map.get(device_role, {})
    
    if device_topics.get('pub'):  # 如果这个设备有发布主题
        for pub_topic in device_topics['pub']:
            # 构建payload schema
            payload_schema = {}
            for dp in final_list:
                if dp.get('mode') in ['ro', 'rw']:  # 只有可上报的DP才会出现在payload中
                    payload_schema[dp['code']] = dp['type']
            
            # 将schema信息存储到统一契约中
            unified_contract['schema'][pub_topic] = {
                "publisher": device_role,
                "payload_schema": payload_schema
            }
```

### 3. 增强开发者节点 ✅

**修复**：从增强的 `unified_communication_contract` 中获取 payload schema

```python
# 【核心修复】为订阅的主题添加 payload schema 信息
for sub_topic in device_topics["sub"]:
    if sub_topic in schema_map:
        schema_info = schema_map[sub_topic]
        publisher = schema_info.get('publisher', 'Unknown')
        payload_schema = schema_info.get('payload_schema', {})
        
        if payload_schema:
            schema_keys = list(payload_schema.keys())
            payload_schema_instructions.append(
                f"Topic '{sub_topic}' (from {publisher}) contains JSON keys: {', '.join(schema_keys)}"
            )
```

### 4. 移除前端轮询错误消息 ✅

**文件**: `app/templates/index.html`

```javascript
// 修改前
} catch (error) {
    stopPolling();
    appendToWorkflowStream(`<div class="stream-entry status-failed">[ERROR] 无法获取工作流状态: ${error.message}。轮询已停止。</div>`);
}

// 修改后
} catch (error) {
    stopPolling();
    // 移除轮询错误消息显示，避免干扰用户
    console.warn('工作流状态轮询失败:', error.message);
}
```

### 5. 移除调试日志长度输出 ✅

**文件**: `app/services/workflow_service.py`, `app/templates/index.html`, `test_logs.py`

移除了所有 `print(f"[DEBUG] 返回日志长度: {len(logs)} 字符")` 类似的调试输出。

## 修复效果

### 修复前的问题流程：
1. 光照采集端生成：`doc["illuminance_lux"] = lux;` ✅
2. 设备切换：DP契约信息丢失 ❌
3. 报警器生成：`if (doc.containsKey("illuminance"))` ❌ (错误的键名)

### 修复后的正确流程：
1. 光照采集端生成：`doc["illuminance_lux"] = lux;` ✅
2. DP设计器增强：将 `illuminance_lux` 合并到 `unified_communication_contract.schema` ✅
3. 设备切换：完整的契约信息被保留 ✅
4. 报警器生成：从契约中获取正确的键名 `illuminance_lux` ✅
5. 契约调试器：在每个关键步骤打印契约信息，便于调试 ✅

## 技术架构改进

### 从"管道契约"升级到"数据契约"

**修改前**：
```json
{
  "topic_map": {
    "光照采集端": {"pub": ["/smart_system/light_collector/data"]},
    "报警器": {"sub": ["/smart_system/light_collector/data"]}
  }
}
```

**修改后**：
```json
{
  "topic_map": {
    "光照采集端": {"pub": ["/smart_system/light_collector/data"]},
    "报警器": {"sub": ["/smart_system/light_collector/data"]}
  },
  "schema": {
    "/smart_system/light_collector/data": {
      "publisher": "光照采集端",
      "payload_schema": {
        "illuminance_lux": "value"
      }
    }
  }
}
```

## 验证方法

1. **运行契约调试器**：在每次 `developer_node` 执行前，会打印完整的契约信息
2. **检查日志输出**：查看是否包含 `payload_schema` 信息
3. **验证生成代码**：第二个设备应该使用正确的 JSON 键名

这个修复从根本上解决了设备间通信失败的问题，确保了"真相"的唯一来源，developer_node 不再需要猜测，而是根据明确的指令来编写解析代码。
