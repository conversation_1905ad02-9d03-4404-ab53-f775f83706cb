# app/services/project_analyzer_service.py
# -*- coding: utf-8 -*-

import json
import os
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage
from langchain_core.output_parsers import JsonOutputParser
from pydantic import SecretStr

from app.services import device_service
from app.models import User

# 从环境变量获取API配置
API_KEY_STR = os.environ.get('LANGCHAIN_API_KEY')
BASE_URL = os.environ.get('LANGCHAIN_BASE_URL', "https://ark.cn-beijing.volces.com/api/v3")

# 检查API_KEY是否设置
if not API_KEY_STR:
    raise ValueError("LANGCHAIN_API_KEY environment variable is not set. Please set it before running the application.")

# 转换为SecretStr类型
API_KEY = SecretStr(API_KEY_STR)

analyzer_model = ChatOpenAI(model="ep-20250223112748-lthgv", temperature=0.1, api_key=API_KEY, base_url=BASE_URL)

def get_analyzer_prompt(raw_text: str, user_devices: list) -> str:
    device_context = "\\n".join(
        [f"- {d.nickname} (ID: {d.internal_device_id}, Model: {d.board_model})" for d in user_devices]
    )
    if not device_context:
        device_context = "用户当前未注册任何设备。"

    prompt = """
<Prompt>
    <Role>你是一位顶级的物联网解决方案架构师，擅长将复杂的项目需求分解为多个协作的设备任务。</Role>
    <Goal>你的任务是将用户涉及多个设备的、口语化的项目构想，转换成一个包含所有设备任务和它们之间通信关系的、结构化的JSON对象。</Goal>
    <Context>
        <UserRawRequest>{}</UserRawRequest>
        <UserRegisteredDevices>
        {}
        </UserRegisteredDevices>
    </Context>
    <Instructions>""".format(raw_text, device_context) + """
    1.  **识别项目全局信息**: 从用户请求中提取总体的 `project_name` (项目名称) 和 `project_description` (对整个多设备系统的描述)。
    2.  **关键词识别规则**: 在解析用户需求时，请特别注意以下关键词和引脚配置：
        * "HC-SR04" + "trig接X号引脚，echo接Y号引脚" → 创建DISTANCE_MEASUREMENT功能的外设，pins: [{"name": "TRIG_PIN", "number": X}, {"name": "ECHO_PIN", "number": Y}]
        * "蜂鸣器" + "X号引脚" → 创建BUZZER功能的外设，pins: [{"name": "CONTROL_PIN", "number": X}]
        * **重要**：如果提到"低电平触发"，必须在description中明确说明"LOW电平激活，HIGH电平关闭"
        * "LED" + "X号引脚" → 创建LED_CONTROL功能的外设，pins: [{"name": "CONTROL_PIN", "number": X}]
        * "低电平触发" → interface设为"DIGITAL"，在描述中注明触发方式
    3.  **识别并定义每个设备 (Device Tasks)**:
        * 在用户的请求中，识别出所有独立工作的物理设备 (例如 "大绿板", "小黑板", "网关")。
        * 为每一个识别出的设备创建一个独立的JSON对象，并放入 `device_tasks` 列表中。
        * 在每个设备对象中：
            * `device_role`: 填写用户描述中对该设备的角色称呼 (e.g., "光照采集端", "报警器")。
            * `internal_device_id`: 根据角色描述，从 `<UserRegisteredDevices>` 列表中选择最匹配的一个设备，将其ID填入。如果找不到匹配或列表为空，留空此字段。
            * `peripherals`: 识别并列出【只属于这个设备】的物理外设 (传感器、执行器等)，并推断其完整配置：
              - `name`: 外设名称
              - `model`: 具体型号 (如果型号未提及，设为 "USERINPUT_REQUIRED")
              - `pins`: 引脚配置数组，每个引脚包含 `name` 和 `number` 字段。根据外设类型使用正确的引脚名称：
                * 蜂鸣器/LED: [{"name": "CONTROL_PIN", "number": 5}]
                * HC-SR04超声波传感器: [{"name": "TRIG_PIN", "number": 23}, {"name": "ECHO_PIN", "number": 21}]
                * I2C设备: [{"name": "SDA_PIN", "number": 21}, {"name": "SCL_PIN", "number": 22}]
                * 如果引脚未提及，设为 [{"name": "PIN", "number": "USERINPUT_REQUIRED"}]
              - `function`: 功能类型 (如 "LIGHT_SENSING", "TEMP_HUMIDITY_SENSING", "MOTION_DETECTION", "DISTANCE_MEASUREMENT", "BUZZER", "LED_CONTROL", "RELAY_SWITCH", "DISPLAY" 等)
              - `interface`: 接口类型 (如 "I2C", "ANALOG", "DIGITAL", "PWM", "ONE_WIRE" 等)
              - `implementationType`: 实现类型 ("SPECIFIC_MODEL" 或 "GENERIC_COMPONENT")
            * `description`: **工程化详细描述**【这个设备自己】的完整功能实现，必须包括：
              - 具体的业务逻辑和条件判断（如阈值、触发条件、比较操作）
              - 传感器读取的频率和处理方式
              - 执行器控制的具体动作、时机和持续时间
              - 数据处理、决策逻辑和控制流程
              - 错误处理和边界情况的处理方式
              - 与云平台或其他设备的数据交互细节
              例如："每5秒使用HC-SR04测量距离，当测量值小于100cm时立即激活蜂鸣器持续鸣响，当测量值大于等于100cm时关闭蜂鸣器，同时将所有距离数据实时发送到涂鸦云进行记录和监控。"
    3.  **定义设备间通信 (Inter-Device Communication)**:
        * 分析设备任务之间的信息流动。
        * 在 `inter_device_communication` 列表中为每一条单向数据流创建一个对象。
        * 每个通信对象应包含：`source_device_role` (发送方角色名), `target_device_role` (接收方角色名), `data_description` (描述传输的数据内容), `protocol` (推断通信协议, 默认为 "MQTT")。
    4.  **格式化输出**: 你的最终输出必须是一个遵循下述格式的、不包含任何额外解释的、单一且有效的JSON对象。
    </Instructions>
    <OutputFormat>
    ```json
    {{
      "project_name": "多设备光照监控报警系统",
      "project_description": "一个由光照采集端和云端报警器组成的系统，当光照过强时通过云平台报警。",
      "device_tasks": [
        {{
          "device_role": "光照采集端",
          "internal_device_id": "device-uuid-of-green-board",
          "peripherals": [
            {{
              "name": "光照传感器",
              "model": "BH1750",
              "pins": [{"name": "SDA_PIN", "number": 21}, {"name": "SCL_PIN", "number": 22}],
              "function": "LIGHT_SENSING",
              "interface": "I2C",
              "implementationType": "SPECIFIC_MODEL"
            }}
          ],
          "description": "每3秒使用BH1750传感器通过I2C接口读取环境光照强度，将读取的lux值通过MQTT协议发送给小黑板进行处理，当传感器读取失败时记录错误并跳过本次发送。"
        }},
        {{
          "device_role": "智能报警器",
          "internal_device_id": "device-uuid-of-green-board",
          "peripherals": [
            {{
              "name": "超声波传感器",
              "model": "HC-SR04",
              "pins": [{"name": "TRIG_PIN", "number": 23}, {"name": "ECHO_PIN", "number": 21}],
              "function": "DISTANCE_MEASUREMENT",
              "interface": "DIGITAL",
              "implementationType": "SPECIFIC_MODEL"
            }},
            {{
              "name": "蜂鸣器",
              "model": "有源蜂鸣器",
              "pins": [{"name": "CONTROL_PIN", "number": 5}],
              "function": "BUZZER",
              "interface": "DIGITAL",
              "implementationType": "GENERIC_COMPONENT"
            }}
          ],
          "description": "每5秒使用HC-SR04超声波传感器测量距离，当测量距离小于100cm时立即激活5号引脚的蜂鸣器并持续鸣响，当距离大于等于100cm时关闭蜂鸣器，同时将所有距离测量数据实时通过MQTT发送到涂鸦云平台进行存储和监控，传感器读取失败时关闭蜂鸣器并记录错误。"
        }}
      ],
      "inter_device_communication": [
        {{
          "source_device_role": "智能报警器",
          "target_device_role": "涂鸦云平台",
          "data_description": "距离测量数据和报警状态 (distance measurement and alarm status)",
          "protocol": "MQTT"
        }}
      ]
    }}
    ```
    </OutputFormat>
</Prompt>
"""
    return prompt

def analyze_requirement(user_id: int, raw_text: str) -> dict:
    user = User.query.get(user_id)
    if not user:
        raise ValueError("User not found")
    user_devices = device_service.get_user_devices(user)
    prompt = get_analyzer_prompt(raw_text, user_devices)
    parser = JsonOutputParser()
    chain = analyzer_model | parser
    try:
        structured_json = chain.invoke([HumanMessage(content=prompt)])
        if 'device_tasks' not in structured_json or not isinstance(structured_json['device_tasks'], list):
            raise ValueError("AI返回的JSON格式不正确，缺少'device_tasks'列表。")
        return structured_json
    except Exception as e:
        print(f"Error invoking LLM or parsing JSON: {e}")
        raise RuntimeError(f"Failed to get a valid JSON response from the analyzer model. Error: {str(e)}")


def analyze_inter_device_communication(device_tasks: list) -> dict:
    """
    【V2 修正后】一个专用的函数，仅根据设备任务列表分析通信关系，并能理解别名。
    """
    # 将设备任务列表格式化为文本，包含角色和昵称，供AI理解
    task_descriptions = "\\n".join(
        [f"- **Role**: {task.get('device_role', 'N/A')}, **Nickname**: {task.get('nickname', 'N/A')}, **Description**: {task.get('description', 'N/A')}" for task in device_tasks]
    )

    prompt = f"""
<Prompt>
    <Role>你是一位顶级的物联网系统架构师，专注于设备间的通信协议和数据流。</Role>
    <Goal>根据下面描述的设备任务列表，分析并定义它们之间所有必要的数据通信链路，请特别注意别名。</Goal>
    <Context>
        <DeviceTasksList>
        {task_descriptions}
        </DeviceTasksList>
    </Context>
    <Instructions>
    1.  **识别实体**: 列表中的每一项代表一个设备，它由唯一的 `Role` 标识。`Nickname` 是该设备的一个别名。
    2.  **解析别名**: 在分析描述时，请理解一个 `Nickname` (例如: "大绿板") 和它对应的 `Role` (例如: "光照采集端") 指的是【完全相同】的实体。不要为它们创建重复的通信路径。
    3.  **分析数据流**: 阅读每个设备的描述，判断哪个设备是数据源，哪个是目标。
    4.  **使用规范角色名**: 在你的最终输出中，必须使用设备的 `Role` 作为 `source_device_role` 和 `target_device_role` 的标识符。不要使用 `Nickname`。
    5.  **创建通信对象**: 为每一条识别出的单向数据流，创建一个包含 `source_device_role`, `target_device_role`, `data_description`, 和 `protocol` (默认为 'MQTT') 的JSON对象。
    6.  **最终格式**: 你的输出必须是一个只包含一个key "inter_device_communication" 的JSON对象，其value是一个包含所有通信对象的列表。如果设备间无需通信，则返回空列表。
    </Instructions>
    <OutputFormat>
    ```json
    {{
      "inter_device_communication": [
        {{
          "source_device_role": "光照采集端",
          "target_device_role": "报警器",
          "data_description": "光照强度数值 (illumination value)",
          "protocol": "MQTT"
        }}
      ]
    }}
    ```
    </OutputFormat>
</Prompt>
"""
    parser = JsonOutputParser()
    chain = analyzer_model | parser
    try:
        result = chain.invoke([HumanMessage(content=prompt)])
        return result
    except Exception as e:
        print(f"Error invoking communication analyzer: {e}")
        raise RuntimeError("Failed to get a valid communication plan from the model.")
