# LCode环境依赖包
# 生成时间: 2025-08-09
# Python版本要求: 3.11.x - 3.12.x (不支持3.13+)
#
# 重要提示: PlatformIO 6.1.19a2 在Python 3.13上可能无法正常安装
# 推荐使用Python 3.11或3.12

python_requires = ">=3.11,<3.13"

# ===== 核心Web框架 =====
Flask==3.0.3
Flask-SQLAlchemy==3.1.1
Flask-Migrate==4.1.0
flask-cors==6.0.1
Werkzeug==3.0.4

# ===== 数据库相关 =====
SQLAlchemy==2.0.35
alembic==1.16.4

# ===== LangChain生态系统 =====
langchain-core==0.3.72
langchain-openai==0.3.28
langsmith==0.4.8

# ===== AI/ML相关 =====
openai==1.97.1
pydantic==2.9.2
pydantic-core==2.27.1
tiktoken==0.9.0

# ===== MQTT通信 =====
paho-mqtt==2.1.0

# ===== LangGraph (如果需要) =====
# 注意: 如果项目使用langgraph，请取消注释下面的行
# langgraph>=0.2.0

# ===== 数据处理 =====
pandas==2.2.2
numpy==1.26.4
openpyxl==3.1.5

# ===== 字符串匹配 =====
thefuzz==0.22.1
python-Levenshtein==0.27.1

# ===== 环境变量管理 =====
python-dotenv==1.0.1

# ===== HTTP请求 =====
requests==2.32.3

# ===== JSON处理 =====
orjson==3.11.1

# ===== 其他工具库 =====
typing-extensions==4.14.1

# ===== 开发工具（可选） =====
# 注意：以下包在生产环境中可能不需要，可根据需要安装
# pytest==8.3.2
# black==24.8.0
# flake8==7.1.1

# ===== PlatformIO =====
# 注意：PlatformIO 6.1.19a2 需要特殊安装命令，见INSTALLATION_GUIDE.md
# 重要：此版本仅支持Python 3.11-3.12，不支持Python 3.13+
# 安装命令：pip install platformio==6.1.19a2
# platformio==6.1.19a2
